"""
Display utilities module for Financial Sentiment Analyzer

Handles all output formatting and display functions.
"""

from config import DISPLAY_CONFIG
from sentiment_analyzer import get_ticker_sector
from policy_analyzer import analyze_policy_categories



def create_hyperlink(url, text):
    """Create a clickable hyperlink for terminal output"""
    if url and url.strip():
        # ANSI escape sequence for hyperlinks: \033]8;;URL\033\\TEXT\033]8;;\033\\
        return f"\033]8;;{url}\033\\{text}\033]8;;\033\\"
    else:
        return text


def create_box(title, content, width=42):
    """Create a bordered box for dashboard display using ASCII characters"""
    lines = []

    # Top border
    lines.append("+" + "-" * (width - 2) + "+")

    # Title line - center the title
    title_padded = f" {title} "
    if len(title_padded) > width - 2:
        title_padded = title_padded[:width-5] + "..."
    title_padding = (width - 2 - len(title_padded)) // 2
    title_line = "|" + " " * title_padding + title_padded + " " * (width - 2 - title_padding - len(title_padded)) + "|"
    lines.append(title_line)

    # Separator
    lines.append("+" + "-" * (width - 2) + "+")

    # Content lines
    for line in content:
        line_text = f" {line}"
        if len(line_text) > width - 3:
            line_text = line_text[:width-6] + "..."
        content_line = "|" + f"{line_text:<{width-3}}" + "|"
        lines.append(content_line)

    # Bottom border
    lines.append("+" + "-" * (width - 2) + "+")

    return lines


def print_dashboard_header():
    """Print the main dashboard header"""
    title = "🚀 FINANCIAL SENTIMENT ANALYZER DASHBOARD"
    header_width = 100  # Fixed width for consistency

    # Ensure title fits
    if len(title) > header_width - 4:
        title = "🚀 FINANCIAL ANALYZER DASHBOARD"

    padding = max(0, (header_width - len(title)) // 2)
    header_line = "=" * header_width

    print("\n" + header_line)
    print(" " * padding + title)
    print(header_line)


def print_side_by_side(left_content, right_content, gap=10):
    """Print two content blocks side by side with proper alignment"""
    # Ensure both contents are lists of strings
    if isinstance(left_content, str):
        left_lines = left_content.split('\n')
    else:
        left_lines = left_content[:]

    if isinstance(right_content, str):
        right_lines = right_content.split('\n')
    else:
        right_lines = right_content[:]

    # For boxes, all lines should be the same width (42 chars)
    # For other content, calculate actual width
    if left_lines and left_lines[0].startswith('+'):
        left_width = 42  # Box width
    else:
        left_width = max(len(line) for line in left_lines) if left_lines else 0

    # Pad to same length
    max_lines = max(len(left_lines), len(right_lines))
    while len(left_lines) < max_lines:
        if left_lines and left_lines[0].startswith('+'):
            left_lines.append(' ' * left_width)  # Empty line for boxes
        else:
            left_lines.append('')
    while len(right_lines) < max_lines:
        right_lines.append('')

    # Print side by side
    for left_line, right_line in zip(left_lines, right_lines):
        # Ensure left line is exactly the expected width
        if len(left_line) < left_width:
            left_line = left_line + ' ' * (left_width - len(left_line))
        elif len(left_line) > left_width:
            left_line = left_line[:left_width]

        print(f"{left_line}{' ' * gap}{right_line}")


def print_three_columns(left_content, middle_content, right_content, col_width=30):
    """Print three content blocks side by side"""
    # Ensure all contents are lists of strings
    if isinstance(left_content, str):
        left_lines = left_content.split('\n')
    else:
        left_lines = left_content[:]

    if isinstance(middle_content, str):
        middle_lines = middle_content.split('\n')
    else:
        middle_lines = middle_content[:]

    if isinstance(right_content, str):
        right_lines = right_content.split('\n')
    else:
        right_lines = right_content[:]

    # Pad to same length
    max_lines = max(len(left_lines), len(middle_lines), len(right_lines))
    while len(left_lines) < max_lines:
        left_lines.append('')
    while len(middle_lines) < max_lines:
        middle_lines.append('')
    while len(right_lines) < max_lines:
        right_lines.append('')

    # Print three columns side by side
    for left_line, middle_line, right_line in zip(left_lines, middle_lines, right_lines):
        # Truncate and pad each column
        left_formatted = left_line[:col_width].ljust(col_width)
        middle_formatted = middle_line[:col_width].ljust(col_width)
        right_formatted = right_line[:col_width]
        print(f"{left_formatted} {middle_formatted} {right_formatted}")


def print_two_panels(left_content, right_content):
    """Print two panels side by side - left panel wider, right panel for news"""
    max_lines = max(len(left_content), len(right_content))

    for i in range(max_lines):
        left_line = left_content[i] if i < len(left_content) else ""
        right_line = right_content[i] if i < len(right_content) else ""

        # Left panel: 65 chars, Right panel: 55 chars
        left_padded = f"{left_line:<65}"
        right_padded = f"{right_line:<55}"

        print(f"{left_padded} │ {right_padded}")


def print_header(title, width=70):
    """Print a formatted header with customizable width"""
    print(f"\n{title}")
    print("=" * width)


def get_mood_emoji(sentiment_score, mood_text):
    """Get appropriate emoji based on sentiment score and mood"""
    if sentiment_score > 0.05 or any(word in mood_text.lower() for word in ['positive', 'supportive', 'bullish']):
        return "😊"
    elif sentiment_score < -0.05 or any(word in mood_text.lower() for word in ['negative', 'bearish', 'cautionary']):
        return "😠"
    else:
        return "😐"


def get_score_indicator(sentiment_score):
    """Get color indicator for sentiment score"""
    if sentiment_score > 0.1:
        return "🟢"  # Green for positive
    elif sentiment_score < -0.1:
        return "🔴"  # Red for negative
    else:
        return "🟡"  # Yellow for neutral


def display_dashboard_overview_single_column(sentiment_analysis, policy_analysis, market_health, market_data, news_stats=None, policy_stats=None):
    """Display main dashboard overview in single column format to avoid hyperlink interference"""
    print_dashboard_header()



    # Create a wide single box with all key metrics
    overview_content = []

    # Market sentiment section
    market_mood = sentiment_analysis.get('market_mood', 'N/A')
    market_score = sentiment_analysis.get('average_sentiment', 0)
    market_emoji = get_mood_emoji(market_score, market_mood)
    market_score_indicator = get_score_indicator(market_score)

    # Format with consistent spacing and alignment
    market_line = f"📊 MARKET: {market_emoji} {market_mood:<8} | {market_score_indicator} {market_score:+.3f} | 📈 {sentiment_analysis.get('positive_percentage', 0):>2.0f}% Pos | 📉 {sentiment_analysis.get('negative_percentage', 0):>2.0f}% Neg | 📊 {sentiment_analysis.get('total_articles', 0):>2} Articles"
    overview_content.append(market_line)

    # Policy sentiment section
    if policy_analysis:
        policy_mood = policy_analysis.get('policy_mood', 'N/A')
        policy_score = policy_analysis.get('policy_sentiment', 0)
        policy_emoji = get_mood_emoji(policy_score, policy_mood)
        policy_score_indicator = get_score_indicator(policy_score)

        policy_line = f"🏛️ POLICY: {policy_emoji} {policy_mood:<8} | {policy_score_indicator} {policy_score:+.3f} | 📄 {policy_analysis.get('total_policy_articles', 0):>2} Articles | ⚡ {len(policy_analysis.get('high_impact_articles', [])):>2} High Impact"
        overview_content.append(policy_line)
    else:
        overview_content.append("🏛️ POLICY: No policy data available")

    # Recommendation section
    if market_health:
        rec_line = f"🚀 RECOMMENDATION: {market_health.get('recommendation', 'N/A'):<4} | {market_health.get('market_trend', 'N/A'):<14} | {market_health.get('average_market_change', 0):+.2f}% Change | Combined: {market_health.get('combined_sentiment', 0):+.3f}"
        overview_content.append(rec_line)
    else:
        overview_content.append("🚀 RECOMMENDATION: No recommendation available")

    # Market indices section
    if market_data:
        indices_parts = []
        for ticker, data in list(market_data.items())[:4]:  # Top 4 indices
            emoji = "📈" if data['price_change'] > 0 else "📉"
            indices_parts.append(f"{emoji} {ticker}: {data['price_change']:+.2f}%")
        indices_line = f"📈 INDICES: {' | '.join(indices_parts)}"
        overview_content.append(indices_line)
    else:
        overview_content.append("📈 INDICES: No market data available")

    # Create single wide box
    overview_box = create_box("📊 SUMMARY", overview_content, 100)

    # Print single box
    for line in overview_box:
        print(line)





def display_market_sentiment(sentiment_analysis):
    """Display market sentiment analysis results"""
    print_header("📊 MARKET SENTIMENT ANALYSIS")
    print(f"  Overall Sentiment: {sentiment_analysis['market_mood']} ({sentiment_analysis['average_sentiment']:+.3f})")
    print(f"  Positive: {sentiment_analysis['positive_percentage']:.0f}% | "
          f"Negative: {sentiment_analysis['negative_percentage']:.0f}% | "
          f"Neutral: {sentiment_analysis['neutral_percentage']:.0f}%")
    print(f"  Total Articles: {sentiment_analysis['total_articles']}")


def display_policy_analysis(policy_analysis):
    """Display government policy analysis results"""
    print_header("🏛️ GOVERNMENT POLICY ANALYSIS")
    print(f"  Policy Sentiment: {policy_analysis['policy_mood']} ({policy_analysis['policy_sentiment']:+.3f})")
    print(f"  Total Policy Articles: {policy_analysis['total_policy_articles']}")

    if policy_analysis['policy_categories']:
        print("  Policy Categories:")
        category_analysis = analyze_policy_categories(policy_analysis)
        for category, data in category_analysis.items():
            print(f"    {data['emoji']} {data['display_name']}: {data['sentiment']:+.3f} ({data['article_count']} articles)")


def display_high_impact_policy_news(policy_analysis):
    """Display high impact policy news"""
    if not policy_analysis['high_impact_articles']:
        return
        
    print_header("⚡ HIGH IMPACT POLICY NEWS", 80)
    
    count = DISPLAY_CONFIG['high_impact_articles_count']
    for i, article in enumerate(policy_analysis['high_impact_articles'][:count], 1):
        impact_emoji = "🔥" if article['impact_level'] == 'High' else "⚠️"
        headline_link = create_hyperlink(article.get('url', ''), article['headline'])
        
        print(f"\n  {i}. {impact_emoji} {article['impact_level']} Impact - Score: {article['impact_score']:.2f}")
        print(f"     Sentiment: {article['polarity']:+.3f} | Weighted: {article['weighted_polarity']:+.3f}")
        print(f"     Source: {article.get('source', 'Unknown')}")
        print(f"     [{article.get('time_ago', 'Unknown time')}]: \"{headline_link}\"")
        
        if i < len(policy_analysis['high_impact_articles'][:count]):
            print("     " + "-" * DISPLAY_CONFIG['separator_length'])


def display_dashboard_sectors_and_tickers(sector_rankings, ticker_rankings, price_changes, current_prices=None):
    """Display sectors, top tickers, and worst tickers in a 3-column format"""
    print("\n" + "-" * 100)

    # Create sectors content
    sectors_content = ["🏭 TOP SECTORS"]
    sectors_content.append("─" * 30)

    for i, sector in enumerate(sector_rankings[:5], 1):
        emoji = "🟢" if sector['average_sentiment'] > 0.1 else "🟡" if sector['average_sentiment'] > 0 else "🔴"
        top_ticker = sector['top_ticker']
        price_change = price_changes.get(top_ticker, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        sectors_content.append(f"{i}. {emoji} {sector['sector'][:12]}")
        sectors_content.append(f"   Score: {sector['sector_strength']:.2f}")
        sectors_content.append(f"   Top: {top_ticker} {price_emoji}{price_change:+.1f}%")
        if i < 5:
            sectors_content.append("")

    # Create top tickers content
    top_tickers_content = ["🏆 TOP TICKERS"]
    top_tickers_content.append("─" * 30)

    for i, ticker in enumerate(ticker_rankings[:5], 1):
        ticker_symbol = ticker['ticker']
        price_change = price_changes.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
        current_price = current_prices.get(ticker_symbol) if current_prices else None

        top_tickers_content.append(f"{i}. {ticker_symbol} ({get_ticker_sector(ticker_symbol)[:8]})")
        top_tickers_content.append(f"   Score: {ticker['overall_score']:.3f}")
        if current_price is not None:
            top_tickers_content.append(f"   ${current_price:.2f} {price_emoji}{price_change:+.1f}%")
        else:
            top_tickers_content.append(f"   Price: {price_emoji}{price_change:+.1f}%")
        if i < 5:
            top_tickers_content.append("")

    # Create worst tickers content - get bottom 5 by sentiment score
    # Sort tickers by sentiment score (lowest first) and take first 5 (worst)
    sorted_by_sentiment = sorted(ticker_rankings, key=lambda x: x['average_sentiment'])
    worst_tickers = sorted_by_sentiment[:5]  # Get the 5 lowest sentiment scores

    worst_tickers_content = ["⚠️ WORST TICKERS"]
    worst_tickers_content.append("─" * 30)

    for i, ticker in enumerate(worst_tickers, 1):
        ticker_symbol = ticker['ticker']
        price_change = price_changes.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
        current_price = current_prices.get(ticker_symbol) if current_prices else None

        worst_tickers_content.append(f"{i}. {ticker_symbol} ({get_ticker_sector(ticker_symbol)[:8]})")
        worst_tickers_content.append(f"   Score: {ticker['average_sentiment']:+.3f}")
        if current_price is not None:
            worst_tickers_content.append(f"   ${current_price:.2f} {price_emoji}{price_change:+.1f}%")
        else:
            worst_tickers_content.append(f"   Price: {price_emoji}{price_change:+.1f}%")
        if i < 5:
            worst_tickers_content.append("")

    # Print all three columns side by side
    print_three_columns(sectors_content, top_tickers_content, worst_tickers_content)


def display_dashboard_news_summary_single_column(news_data, sentiment_scores, sentiment_details, policy_analysis, news_limit=10):
    """Display recent news and policy highlights in single column format to avoid hyperlink interference"""
    print("\n" + "=" * 100)
    print("📰 RECENT NEWS & POLICY HIGHLIGHTS")
    print("=" * 100)

    # Market News Section
    print("\n🕒 RECENT MARKET NEWS:")
    print("-" * 50)

    # Combine and sort by recency
    combined_data = []
    for i, article in enumerate(news_data[:20]):  # Limit to recent articles
        if i < len(sentiment_scores):
            combined_data.append({
                'article': article,
                'sentiment_score': sentiment_scores[i]
            })

    combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)

    for i, item in enumerate(combined_data[:news_limit], 1):
        article = item['article']
        sentiment_score = item['sentiment_score']

        # Sentiment emoji
        if sentiment_score > 0.1:
            emoji = "🟢"
        elif sentiment_score > -0.1:
            emoji = "🟡"
        else:
            emoji = "🔴"

        ticker = article.get('ticker', 'N/A')
        time_info = article.get('time_ago', 'Unknown')
        headline = article['headline'][:80] + "..." if len(article['headline']) > 80 else article['headline']

        # Create clickable headline
        headline_link = create_hyperlink(article.get('url', ''), headline)

        print(f"{i}. {emoji} {ticker} [{time_info}]: {headline_link}")

    # Policy Highlights Section
    print("\n🏛️ POLICY HIGHLIGHTS:")
    print("-" * 50)

    if policy_analysis and policy_analysis.get('high_impact_articles'):
        for i, article in enumerate(policy_analysis['high_impact_articles'][:5], 1):
            impact_emoji = "🔥" if article['impact_level'] == 'High' else "⚠️"
            headline = article['headline'][:80] + "..." if len(article['headline']) > 80 else article['headline']
            time_info = article.get('time_ago', 'Unknown')

            # Create clickable headline
            headline_link = create_hyperlink(article.get('url', ''), headline)

            print(f"{i}. {impact_emoji} {article['impact_level']} (Score: {article['impact_score']:.1f}) [{time_info}]: {headline_link}")
    else:
        print("No high-impact policy news available")





def display_sector_performance(sector_rankings, price_changes):
    """Display top sector performance"""
    print_header("🏭 TOP SECTOR PERFORMANCE")

    count = DISPLAY_CONFIG['top_sectors_count']
    for i, sector in enumerate(sector_rankings[:count], 1):
        emoji = "🟢" if sector['average_sentiment'] > 0.1 else "🟡" if sector['average_sentiment'] > 0 else "🔴"
        top_ticker = sector['top_ticker']
        price_change = price_changes.get(top_ticker, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        print(f"  {i}. {emoji} {sector['sector']} - Strength: {sector['sector_strength']:.3f}")
        print(f"     Avg Sentiment: {sector['average_sentiment']:+.3f} | "
              f"Tickers: {sector['ticker_count']} | "
              f"Positive: {sector['positive_percentage']:.0f}%")
        print(f"     Top Performer: {top_ticker} (Score: {sector['top_ticker_score']:.3f}) "
              f"{price_emoji} {price_change:+.2f}%")


def display_top_tickers(ticker_rankings, price_changes, recommendations):
    """Display top sentiment tickers"""
    print_header("🏆 TOP 5 BEST SENTIMENT TICKERS", 80)

    count = DISPLAY_CONFIG['top_tickers_count']
    for i, ticker in enumerate(ticker_rankings[:count], 1):
        sector = get_ticker_sector(ticker['ticker'])
        ticker_symbol = ticker['ticker']
        price_change = price_changes.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        # Get analyst recommendation
        rec_data = recommendations.get(ticker_symbol, {})
        analyst_rec = rec_data.get('recommendation', 'N/A')
        upside = rec_data.get('upside_potential', None)

        # Create recommendation display
        rec_display = f"Analyst: {analyst_rec}"
        if upside is not None:
            upside_emoji = "🎯" if upside > 10 else "📊" if upside > 0 else "⚠️"
            rec_display += f" {upside_emoji} {upside:+.1f}% upside"

        # Create clickable headline
        headline_link = create_hyperlink(ticker['best_headline_url'], ticker['best_headline'])

        print(f"\n  {i}. {ticker_symbol} ({sector}) - Score: {ticker['overall_score']:.3f} {price_emoji} {price_change:+.2f}%")
        print(f"     Sentiment: {ticker['average_sentiment']:+.3f} | "
              f"Articles: {ticker['total_articles']} | "
              f"Positive: {ticker['positive_percentage']:.0f}%")
        print(f"     {rec_display}")
        print(f"     Best News [{ticker['best_headline_time']}]: \"{headline_link}\"")
        print(f"     Published: {ticker['best_headline_datetime']}")

        # Add separator line between tickers (except for the last one)
        if i < count:
            print("     " + "-" * DISPLAY_CONFIG['separator_length'])


def display_negative_tickers(ticker_rankings, price_changes, recommendations):
    """Display tickers with negative sentiment"""
    negative_tickers = [t for t in ticker_rankings if t['average_sentiment'] < -0.05]
    if not negative_tickers:
        print("\n✅ No tickers with significantly negative sentiment found!")
        return

    print_header("⚠️ TICKERS TO WATCH (Negative Sentiment)", 80)

    count = DISPLAY_CONFIG['negative_tickers_count']
    for i, ticker in enumerate(negative_tickers[:count], 1):
        sector = get_ticker_sector(ticker['ticker'])
        ticker_symbol = ticker['ticker']
        price_change = price_changes.get(ticker_symbol, 0.0)
        price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

        # Get analyst recommendation
        rec_data = recommendations.get(ticker_symbol, {})
        analyst_rec = rec_data.get('recommendation', 'N/A')
        upside = rec_data.get('upside_potential', None)

        # Create recommendation display
        rec_display = f"Analyst: {analyst_rec}"
        if upside is not None:
            upside_emoji = "🎯" if upside > 10 else "📊" if upside > 0 else "⚠️"
            rec_display += f" {upside_emoji} {upside:+.1f}% upside"

        # Create clickable headline
        headline_link = create_hyperlink(ticker['worst_headline_url'], ticker['worst_headline'])

        print(f"\n  {i}. {ticker_symbol} ({sector}) - Score: {ticker['average_sentiment']:+.3f} {price_emoji} {price_change:+.2f}%")
        print(f"     Negative: {ticker['negative_percentage']:.0f}% | Articles: {ticker['total_articles']}")
        print(f"     {rec_display}")
        print(f"     Concerning [{ticker['worst_headline_time']}]: \"{headline_link}\"")
        print(f"     Published: {ticker['worst_headline_datetime']}")

        # Add separator line between tickers (except for the last one)
        if i < len(negative_tickers[:count]):
            print("     " + "-" * DISPLAY_CONFIG['separator_length'])


def display_combined_analysis(market_health):
    """Display combined market and policy analysis"""
    print_header("🎯 COMBINED MARKET & POLICY ANALYSIS")
    
    combined_sentiment = market_health.get('combined_sentiment', 0)
    policy_influence = market_health.get('policy_influence', 0)
    
    print(f"  Combined Score: {combined_sentiment:+.3f}")
    print(f"  Policy Influence: {policy_influence:+.3f}")
    
    # Policy impact assessment
    if abs(policy_influence) > 0.1:
        if policy_influence > 0:
            policy_impact = "🟢 Government policies are providing significant market support"
        else:
            policy_impact = "🔴 Government policies are creating market headwinds"
    elif abs(policy_influence) > 0.05:
        if policy_influence > 0:
            policy_impact = "🟡 Government policies are mildly supportive"
        else:
            policy_impact = "🟡 Government policies are creating mild concerns"
    else:
        policy_impact = "⚪ Government policies have neutral market impact"
    
    print(f"  Policy Assessment: {policy_impact}")


def display_recommendation(market_health):
    """Display trading recommendation"""
    print_header("🚀 RECOMMENDATION")
    print(f"  {market_health['recommendation']}")
    print(f"  Market Trend: {market_health['market_trend']} ({market_health['average_market_change']:+.2f}%)")


def display_market_indices(market_data):
    """Display market indices performance"""
    print_header("📈 MARKET INDICES PERFORMANCE")
    for index_ticker, data in market_data.items():
        emoji = "📈" if data['price_change'] > 0 else "📉"
        print(f"  {emoji} {data['name']} ({index_ticker}): {data['price_change']:+.2f}%")





def display_sentiment_ranked_timeline(news_data, sentiment_scores, sentiment_details, limit=15):
    """Display news timeline chronologically with sentiment scores"""
    print_header("🕒 RECENT NEWS TIMELINE")

    # Combine news data with sentiment scores
    combined_data = []
    for i, article in enumerate(news_data):
        if i < len(sentiment_scores) and i < len(sentiment_details):
            combined_data.append({
                'article': article,
                'sentiment_score': sentiment_scores[i],
                'sentiment_detail': sentiment_details[i]
            })

    # Sort by recency (most recent first)
    combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)

    for i, item in enumerate(combined_data[:limit], 1):
        article = item['article']
        sentiment_score = item['sentiment_score']

        time_info = article.get('time_ago', 'Unknown time')
        ticker = article.get('ticker', 'N/A')
        headline = article['headline'][:75] + "..." if len(article['headline']) > 75 else article['headline']
        headline_link = create_hyperlink(article.get('url', ''), headline)

        # Sentiment emoji and color coding
        if sentiment_score > 0.1:
            sentiment_emoji = "🟢"
            sentiment_label = "Positive"
        elif sentiment_score > 0.05:
            sentiment_emoji = "🟡"
            sentiment_label = "Mildly Positive"
        elif sentiment_score > -0.05:
            sentiment_emoji = "⚪"
            sentiment_label = "Neutral"
        elif sentiment_score > -0.1:
            sentiment_emoji = "🟡"
            sentiment_label = "Mildly Negative"
        else:
            sentiment_emoji = "🔴"
            sentiment_label = "Negative"

        print(f"  {i}. {sentiment_emoji} [{time_info}] {ticker}: \"{headline_link}\"")
        print(f"     Sentiment: {sentiment_score:+.3f} ({sentiment_label}) | Published: {article.get('datetime', 'Unknown')}")

        # Add separator line between articles (except for the last one)
        if i < min(len(combined_data), limit):
            print()


def print_help():
    """Print comprehensive help information"""
    help_text = """
🚀 FINANCIAL SENTIMENT ANALYZER WITH POLICY INTEGRATION

DESCRIPTION:
    A comprehensive tool for analyzing market sentiment from news sources and 
    government policy announcements to provide trading insights.

USAGE:
    python financial_analyzer.py [options]

OPTIONS:
    --help, -h              Show this help message
    --market-only           Run only market sentiment analysis
    --policy-only           Run only government policy analysis
    --sectors               Show detailed sector analysis
    --tickers               Show detailed ticker rankings
    --recommendations       Show analyst recommendations
    --indices               Show market indices performance
    --timeline              Show recent news timeline with sentiment scores (10-15 items)
    --dashboard             Show compact dashboard view (default for full analysis)
    --detailed              Show traditional detailed output format
    --quick                 Quick analysis (fewer sources, faster)
    --verbose               Verbose output with debug information

EXAMPLES:
    python financial_analyzer.py
        Run full analysis with compact dashboard view

    python financial_analyzer.py --detailed
        Run full analysis with traditional detailed output

    python financial_analyzer.py --market-only
        Analyze only market sentiment (no government policy)

    python financial_analyzer.py --policy-only
        Analyze only government policy impact

    python financial_analyzer.py --quick
        Quick analysis using fewer data sources

    python financial_analyzer.py --sectors --tickers
        Show detailed sector and ticker analysis

    python financial_analyzer.py --timeline
        Show only recent news timeline with sentiment scores (15 items)

    python financial_analyzer.py --timeline --quick
        Quick news timeline with sentiment scores (faster, fewer sources)

FEATURES:
    • Market sentiment analysis from 80+ major tickers
    • Government policy impact analysis from Federal Reserve feeds
    • Sector performance rankings
    • Individual ticker sentiment scores
    • Analyst recommendations integration
    • Combined market and policy recommendations
    • Real-time news timeline with clickable links

DATA SOURCES:
    • Yahoo Finance (stock news and prices)
    • Federal Reserve RSS feeds (monetary policy)
    • Government regulatory announcements
    • Analyst recommendations and price targets

OUTPUT SECTIONS:
    • Market Sentiment Analysis
    • Government Policy Analysis  
    • High Impact Policy News
    • Top Sector Performance
    • Best/Worst Sentiment Tickers
    • Combined Analysis & Recommendations
    • Market Indices Performance
    • Recent News Timeline

For more information, see README.md
"""
    print(help_text)


def display_multi_ticker_analysis(multi_ticker_articles, cross_ticker_analysis):
    """Display multi-ticker sentiment analysis results"""
    if not multi_ticker_articles:
        return

    print("\n" + "=" * 100)
    print("🔄 MULTI-TICKER SENTIMENT ANALYSIS")
    print("=" * 100)

    print(f"\n📊 SUMMARY:")
    print(f"   • Found {len(multi_ticker_articles)} articles mentioning multiple tickers")
    print(f"   • {len(cross_ticker_analysis['sentiment_conflicts'])} articles with conflicting sentiments")
    print(f"   • {len(cross_ticker_analysis['ticker_pairs'])} unique ticker pairs detected")

    # Show sentiment conflicts
    if cross_ticker_analysis['sentiment_conflicts']:
        print(f"\n⚠️  SENTIMENT CONFLICTS:")
        print("-" * 50)

        for i, conflict in enumerate(cross_ticker_analysis['sentiment_conflicts'][:5], 1):
            headline = conflict['headline'][:70] + "..." if len(conflict['headline']) > 70 else conflict['headline']

            positive_str = ", ".join(conflict['positive_tickers'])
            negative_str = ", ".join(conflict['negative_tickers'])

            print(f"{i}. {headline}")
            print(f"   🟢 Positive for: {positive_str}")
            print(f"   🔴 Negative for: {negative_str}")
            if conflict['neutral_tickers']:
                neutral_str = ", ".join(conflict['neutral_tickers'])
                print(f"   ⚪ Neutral for: {neutral_str}")
            print(f"   📅 {conflict['time_ago']}")
            print()

    # Show most common ticker pairs
    if cross_ticker_analysis['ticker_pairs']:
        print(f"\n🔗 MOST MENTIONED TICKER PAIRS:")
        print("-" * 50)

        for i, (pair, data) in enumerate(list(cross_ticker_analysis['ticker_pairs'].items())[:5], 1):
            print(f"{i}. {pair}: {data['count']} articles")

            # Show recent sentiment patterns
            recent_patterns = data['sentiment_patterns'][-3:]  # Last 3 patterns
            for pattern in recent_patterns:
                print(f"   • {pattern}")
            print()


def display_news_panel_with_tickers(news_data, sentiment_scores, sentiment_details, multi_ticker_articles, news_limit=20):
    """Display news panel with all associated tickers for each article"""
    news_content = []
    news_content.append("📰 RECENT MARKET NEWS")
    news_content.append("═" * 45)
    news_content.append("")

    # Create a mapping of articles to their multi-ticker data
    multi_ticker_map = {}
    for mt_article in multi_ticker_articles:
        article_index = mt_article['article_index']
        multi_ticker_map[article_index] = mt_article

    # Combine and sort by recency
    combined_data = []
    for i, article in enumerate(news_data[:30]):  # Look at more articles
        if i < len(sentiment_scores):
            combined_data.append({
                'article': article,
                'sentiment_score': sentiment_scores[i],
                'article_index': i,
                'sentiment_detail': sentiment_details[i] if i < len(sentiment_details) else {}
            })

    combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)

    for i, item in enumerate(combined_data[:news_limit], 1):
        article = item['article']
        sentiment_score = item['sentiment_score']
        article_index = item['article_index']
        sentiment_detail = item['sentiment_detail']

        # Sentiment emoji
        if sentiment_score > 0.1:
            emoji = "🟢"
        elif sentiment_score > -0.1:
            emoji = "🟡"
        else:
            emoji = "🔴"

        # Get primary ticker
        primary_ticker = article.get('ticker', 'N/A')
        time_info = article.get('time_ago', 'Unknown')

        # Truncate headline for panel width
        headline = article['headline'][:47] + "..." if len(article['headline']) > 47 else article['headline']

        # Check if this is a multi-ticker article
        mentioned_tickers = []
        ticker_sentiments = {}

        if article_index in multi_ticker_map:
            mt_data = multi_ticker_map[article_index]
            mentioned_tickers = mt_data['mentioned_tickers']
            ticker_sentiments = mt_data['ticker_sentiments']
        elif 'mentioned_tickers' in sentiment_detail:
            mentioned_tickers = sentiment_detail['mentioned_tickers']
            ticker_sentiments = sentiment_detail.get('ticker_sentiments', {})

        # Display article
        news_content.append(f"{i:2d}. {emoji} [{time_info}]")
        news_content.append(f"    {headline}")

        # Show tickers with their individual sentiments
        if len(mentioned_tickers) > 1:
            # Multi-ticker article
            ticker_line = "    🔗 "
            ticker_parts = []
            for ticker in mentioned_tickers[:3]:  # Show up to 3 tickers to fit width
                if ticker in ticker_sentiments:
                    ticker_sentiment = ticker_sentiments[ticker]
                    if ticker_sentiment['sentiment_category'] == 'Positive':
                        ticker_emoji = "🟢"
                    elif ticker_sentiment['sentiment_category'] == 'Negative':
                        ticker_emoji = "🔴"
                    else:
                        ticker_emoji = "🟡"
                    ticker_parts.append(f"{ticker_emoji}{ticker}")
                else:
                    ticker_parts.append(f"⚪{ticker}")

            if len(mentioned_tickers) > 3:
                ticker_parts.append(f"+{len(mentioned_tickers)-3}")

            ticker_line += " ".join(ticker_parts)
            news_content.append(ticker_line)
        else:
            # Single ticker article
            news_content.append(f"    📊 {primary_ticker}")

        # Only add spacing for first 15 articles to save space
        if i <= 15:
            news_content.append("")  # Spacing between articles

    return news_content


def display_two_panel_dashboard(sentiment_analysis, policy_analysis, market_health, market_data,
                               sector_rankings, ticker_rankings, price_changes, news_data,
                               sentiment_scores, sentiment_details, news_stats=None, policy_stats=None,
                               news_limit=20, current_prices=None, multi_ticker_articles=None, cross_ticker_analysis=None):
    """Display dashboard with left panel for main content and right panel for news"""

    print_dashboard_header()

    # Create left panel content (main dashboard)
    left_content = []

    # Summary section
    left_content.append("📊 SUMMARY")
    left_content.append("═" * 65)

    # Market sentiment
    market_mood = sentiment_analysis.get('market_mood', 'N/A')
    market_score = sentiment_analysis.get('average_sentiment', 0)
    market_emoji = get_mood_emoji(market_score, market_mood)
    market_score_indicator = get_score_indicator(market_score)

    left_content.append(f"📊 MARKET: {market_emoji} {market_mood} | {market_score_indicator} {market_score:+.3f}")
    left_content.append(f"   📈 {sentiment_analysis.get('positive_percentage', 0):.0f}% Pos | 📉 {sentiment_analysis.get('negative_percentage', 0):.0f}% Neg | 📊 {sentiment_analysis.get('total_articles', 0)} Articles")

    # Policy sentiment
    if policy_analysis:
        policy_mood = policy_analysis.get('policy_mood', 'N/A')
        policy_score = policy_analysis.get('policy_sentiment', 0)
        policy_emoji = get_mood_emoji(policy_score, policy_mood)
        policy_score_indicator = get_score_indicator(policy_score)

        left_content.append(f"🏛️ POLICY: {policy_emoji} {policy_mood} | {policy_score_indicator} {policy_score:+.3f}")
        left_content.append(f"   📄 {policy_analysis.get('total_policy_articles', 0)} Articles | ⚡ {len(policy_analysis.get('high_impact_articles', []))} High Impact")

    # Recommendation
    if market_health:
        left_content.append(f"🚀 REC: {market_health.get('recommendation', 'N/A')} | {market_health.get('market_trend', 'N/A')}")
        left_content.append(f"   📈 {market_health.get('average_market_change', 0):+.2f}% Change | Combined: {market_health.get('combined_sentiment', 0):+.3f}")

    left_content.append("")

    # Sectors and Tickers section
    left_content.append("🏭 TOP SECTORS & TICKERS")
    left_content.append("═" * 65)

    # Top sectors (compact)
    if sector_rankings:
        left_content.append("🏭 SECTORS:")
        for i, sector in enumerate(sector_rankings[:3], 1):
            emoji = "🟢" if sector['average_sentiment'] > 0.1 else "🟡" if sector['average_sentiment'] > 0 else "🔴"
            left_content.append(f"  {i}. {emoji} {sector['sector'][:12]} ({sector['sector_strength']:.2f})")

    left_content.append("")

    # Top tickers (compact)
    if ticker_rankings:
        left_content.append("🏆 TOP TICKERS:")
        for i, ticker in enumerate(ticker_rankings[:5], 1):
            ticker_symbol = ticker['ticker']
            price_change = price_changes.get(ticker_symbol, 0.0)
            price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            current_price = current_prices.get(ticker_symbol) if current_prices else None

            price_str = f"${current_price:.2f}" if current_price else "N/A"
            left_content.append(f"  {i}. {ticker_symbol} {price_emoji} {price_str} ({price_change:+.1f}%)")
            left_content.append(f"     Score: {ticker['overall_score']:.3f}")

    left_content.append("")

    # Multi-ticker analysis (compact)
    if multi_ticker_articles and cross_ticker_analysis:
        left_content.append("🔄 MULTI-TICKER ANALYSIS")
        left_content.append("═" * 65)
        left_content.append(f"📊 {len(multi_ticker_articles)} multi-ticker articles")
        left_content.append(f"⚠️ {len(cross_ticker_analysis['sentiment_conflicts'])} conflicts")
        left_content.append(f"🔗 {len(cross_ticker_analysis['ticker_pairs'])} ticker pairs")

        # Show top conflicts
        if cross_ticker_analysis['sentiment_conflicts']:
            left_content.append("")
            left_content.append("⚠️ CONFLICTS:")
            for conflict in cross_ticker_analysis['sentiment_conflicts'][:2]:
                pos_tickers = ", ".join(conflict['positive_tickers'][:2])
                neg_tickers = ", ".join(conflict['negative_tickers'][:2])
                left_content.append(f"  🟢 {pos_tickers} vs 🔴 {neg_tickers}")

        left_content.append("")

    # Market indices
    if market_data:
        left_content.append("📈 MARKET INDICES")
        left_content.append("═" * 65)
        for ticker, data in list(market_data.items())[:4]:
            emoji = "📈" if data['price_change'] > 0 else "📉"
            left_content.append(f"{emoji} {ticker}: {data['price_change']:+.2f}%")

    # Create right panel content (news)
    right_content = display_news_panel_with_tickers(news_data, sentiment_scores, sentiment_details, multi_ticker_articles, news_limit)

    # Print two panels side by side
    print_two_panels(left_content, right_content)


def display_full_dashboard_single_column(sentiment_analysis, policy_analysis, market_health, market_data,
                                        sector_rankings, ticker_rankings, price_changes, news_data,
                                        sentiment_scores, sentiment_details, news_stats=None, policy_stats=None,
                                        news_limit=10, current_prices=None, multi_ticker_articles=None, cross_ticker_analysis=None):
    """Display the complete dashboard - now uses two-panel layout"""

    # Use the new two-panel layout
    display_two_panel_dashboard(sentiment_analysis, policy_analysis, market_health, market_data,
                               sector_rankings, ticker_rankings, price_changes, news_data,
                               sentiment_scores, sentiment_details, news_stats, policy_stats,
                               news_limit, current_prices, multi_ticker_articles, cross_ticker_analysis)

    # Footer with key insights
    footer_line = "=" * 100
    print("\n" + footer_line)
    if market_health:
        combined_sentiment = market_health.get('combined_sentiment', 0)
        policy_influence = market_health.get('policy_influence', 0)

        insights = []
        if abs(combined_sentiment) > 0.1:
            if combined_sentiment > 0:
                insights.append("🟢 Strong positive market sentiment")
            else:
                insights.append("🔴 Strong negative market sentiment")

        if abs(policy_influence) > 0.05:
            if policy_influence > 0:
                insights.append("🏛️ Policy support detected")
            else:
                insights.append("🏛️ Policy headwinds present")

        if not insights:
            insights.append("📊 Market sentiment is neutral")

        print("💡 KEY INSIGHTS: " + " | ".join(insights))

    print(footer_line)



