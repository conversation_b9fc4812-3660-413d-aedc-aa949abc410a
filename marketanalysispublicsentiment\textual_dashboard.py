#!/usr/bin/env python3
"""
Modern Textual-based dashboard for Financial Sentiment Analyzer
"""

from textual.app import App, ComposeResult
from textual.containers import Container, Horizontal, Vertical, ScrollableContainer
from textual.widgets import (
    Header, Footer, Static, DataTable, Label, 
    ProgressBar, Button, Tabs, TabPane
)
from textual.reactive import reactive
from textual.timer import Timer
from textual import events
from rich.text import Text
from rich.table import Table
from rich.panel import Panel
from rich.console import Console
from datetime import datetime
import asyncio


class SummaryPanel(Static):
    """Panel showing market and policy summary"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "📊 Summary"
    
    def update_data(self, sentiment_analysis, policy_analysis, market_health):
        """Update the summary panel with new data"""
        console = Console()
        
        # Market sentiment
        market_mood = sentiment_analysis.get('market_mood', 'N/A')
        market_score = sentiment_analysis.get('average_sentiment', 0)
        market_emoji = self._get_mood_emoji(market_score, market_mood)
        
        # Policy sentiment
        policy_mood = policy_analysis.get('policy_mood', 'N/A') if policy_analysis else 'N/A'
        policy_score = policy_analysis.get('policy_sentiment', 0) if policy_analysis else 0
        policy_emoji = self._get_mood_emoji(policy_score, policy_mood)
        
        # Recommendation
        recommendation = market_health.get('recommendation', 'N/A') if market_health else 'N/A'
        
        # Create rich content
        table = Table.grid(padding=1)
        table.add_column(style="bold")
        table.add_column()
        
        table.add_row(
            f"📊 Market:", 
            f"{market_emoji} {market_mood} ({market_score:+.3f})"
        )
        
        if policy_analysis:
            table.add_row(
                f"🏛️ Policy:", 
                f"{policy_emoji} {policy_mood} ({policy_score:+.3f})"
            )
        
        table.add_row(
            f"🚀 Recommendation:", 
            f"{recommendation}"
        )
        
        # Update the widget content
        self.update(table)
    
    def _get_mood_emoji(self, sentiment_score, mood_text):
        """Get appropriate emoji based on sentiment"""
        if sentiment_score > 0.05:
            return "😊"
        elif sentiment_score < -0.05:
            return "😠"
        else:
            return "😐"


class NewsPanel(ScrollableContainer):
    """Panel showing recent news with multi-ticker information"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "📰 Recent Market News"
    
    def update_data(self, news_data, sentiment_scores, sentiment_details, multi_ticker_articles):
        """Update the news panel with new data"""
        # Clear existing content
        self.remove_children()
        
        # Create a mapping of articles to their multi-ticker data
        multi_ticker_map = {}
        for mt_article in multi_ticker_articles:
            article_index = mt_article['article_index']
            multi_ticker_map[article_index] = mt_article
        
        # Combine and sort by recency
        combined_data = []
        for i, article in enumerate(news_data[:30]):
            if i < len(sentiment_scores):
                combined_data.append({
                    'article': article,
                    'sentiment_score': sentiment_scores[i],
                    'article_index': i,
                    'sentiment_detail': sentiment_details[i] if i < len(sentiment_details) else {}
                })
        
        combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)
        
        # Add news items
        for i, item in enumerate(combined_data[:20], 1):
            article = item['article']
            sentiment_score = item['sentiment_score']
            article_index = item['article_index']
            sentiment_detail = item['sentiment_detail']
            
            # Create news item widget
            news_item = self._create_news_item(
                i, article, sentiment_score, article_index, 
                multi_ticker_map, sentiment_detail
            )
            self.mount(news_item)
    
    def _create_news_item(self, index, article, sentiment_score, article_index, multi_ticker_map, sentiment_detail):
        """Create a single news item widget"""
        # Sentiment emoji
        if sentiment_score > 0.1:
            emoji = "🟢"
        elif sentiment_score > -0.1:
            emoji = "🟡"
        else:
            emoji = "🔴"
        
        # Get ticker information
        primary_ticker = article.get('ticker', 'N/A')
        time_info = article.get('time_ago', 'Unknown')
        headline = article['headline']
        
        # Check for multi-ticker information
        mentioned_tickers = []
        ticker_sentiments = {}
        
        if article_index in multi_ticker_map:
            mt_data = multi_ticker_map[article_index]
            mentioned_tickers = mt_data['mentioned_tickers']
            ticker_sentiments = mt_data['ticker_sentiments']
        elif 'mentioned_tickers' in sentiment_detail:
            mentioned_tickers = sentiment_detail['mentioned_tickers']
            ticker_sentiments = sentiment_detail.get('ticker_sentiments', {})
        
        # Create content - escape markup characters
        content_lines = []
        # Escape square brackets to prevent markup interpretation
        safe_time_info = time_info.replace("[", "\\[").replace("]", "\\]")
        safe_headline = headline.replace("[", "\\[").replace("]", "\\]")

        content_lines.append(f"{index:2d}. {emoji} \\[{safe_time_info}\\]")
        content_lines.append(f"    {safe_headline}")

        # Show tickers
        if len(mentioned_tickers) > 1:
            # Multi-ticker article
            ticker_parts = []
            for ticker in mentioned_tickers[:4]:
                if ticker in ticker_sentiments:
                    ticker_sentiment = ticker_sentiments[ticker]
                    if ticker_sentiment['sentiment_category'] == 'Positive':
                        ticker_emoji = "🟢"
                    elif ticker_sentiment['sentiment_category'] == 'Negative':
                        ticker_emoji = "🔴"
                    else:
                        ticker_emoji = "🟡"
                    ticker_parts.append(f"{ticker_emoji}{ticker}")
                else:
                    ticker_parts.append(f"⚪{ticker}")

            if len(mentioned_tickers) > 4:
                ticker_parts.append(f"+{len(mentioned_tickers)-4}")

            content_lines.append(f"    🔗 {' '.join(ticker_parts)}")
        else:
            content_lines.append(f"    📊 {primary_ticker}")

        return Static("\n".join(content_lines), classes="news-item", markup=False)


class TickersPanel(Static):
    """Panel showing top tickers and sectors"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "🏆 Top Performers"
    
    def update_data(self, sector_rankings, ticker_rankings, price_changes, current_prices):
        """Update the tickers panel with new data"""
        table = Table.grid(padding=1)
        table.add_column("Rank", style="bold")
        table.add_column("Ticker")
        table.add_column("Price")
        table.add_column("Score")
        
        # Add top tickers
        for i, ticker in enumerate(ticker_rankings[:8], 1):
            ticker_symbol = ticker['ticker']
            price_change = price_changes.get(ticker_symbol, 0.0)
            current_price = current_prices.get(ticker_symbol) if current_prices else None
            
            price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            price_str = f"${current_price:.2f}" if current_price else "N/A"
            
            table.add_row(
                f"{i}.",
                f"{ticker_symbol}",
                f"{price_emoji} {price_str} ({price_change:+.1f}%)",
                f"{ticker['overall_score']:.3f}"
            )
        
        self.update(table)


class MultiTickerPanel(Static):
    """Panel showing multi-ticker analysis"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "🔄 Multi-Ticker Analysis"
    
    def update_data(self, multi_ticker_articles, cross_ticker_analysis):
        """Update the multi-ticker panel with new data"""
        if not multi_ticker_articles:
            self.update("No multi-ticker articles found")
            return
        
        content_lines = []
        content_lines.append(f"📊 {len(multi_ticker_articles)} multi-ticker articles")
        content_lines.append(f"⚠️ {len(cross_ticker_analysis['sentiment_conflicts'])} conflicts")
        content_lines.append(f"🔗 {len(cross_ticker_analysis['ticker_pairs'])} ticker pairs")
        
        # Show conflicts
        if cross_ticker_analysis['sentiment_conflicts']:
            content_lines.append("\n⚠️ CONFLICTS:")
            for conflict in cross_ticker_analysis['sentiment_conflicts'][:3]:
                pos_tickers = ", ".join(conflict['positive_tickers'][:2])
                neg_tickers = ", ".join(conflict['negative_tickers'][:2])
                content_lines.append(f"  🟢 {pos_tickers} vs 🔴 {neg_tickers}")
        
        self.update("\n".join(content_lines))


class FinancialDashboard(App):
    """Main Textual dashboard application"""
    
    CSS = """
    .news-item {
        margin: 1;
        padding: 1;
    }
    
    #left-panel {
        width: 1fr;
        margin: 1;
    }
    
    #right-panel {
        width: 2fr;
        margin: 1;
    }
    
    #summary-panel {
        height: 8;
        margin: 1;
    }
    
    #tickers-panel {
        height: 12;
        margin: 1;
    }
    
    #multi-ticker-panel {
        height: 8;
        margin: 1;
    }
    
    #news-panel {
        margin: 1;
    }
    """
    
    TITLE = "🚀 Financial Sentiment Analyzer"
    SUB_TITLE = "Real-time Market & Policy Analysis"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.data_update_timer = None
        
    def compose(self) -> ComposeResult:
        """Create the dashboard layout"""
        yield Header()
        
        with Horizontal():
            # Left panel - Summary and analysis
            with Vertical(id="left-panel"):
                yield SummaryPanel(id="summary-panel")
                yield TickersPanel(id="tickers-panel") 
                yield MultiTickerPanel(id="multi-ticker-panel")
            
            # Right panel - News feed
            with Vertical(id="right-panel"):
                yield NewsPanel(id="news-panel")
        
        yield Footer()
    
    def on_mount(self) -> None:
        """Called when the app is mounted"""
        # Start periodic data updates
        self.set_interval(60, self.update_dashboard_data)  # Update every 60 seconds
        
        # Initial data load
        self.call_later(self.update_dashboard_data)
    
    async def update_dashboard_data(self) -> None:
        """Update all dashboard data"""
        try:
            # Import here to avoid circular imports
            from financial_analyzer import analyze_all_data, fetch_all_data
            
            # Fetch new data
            news_data, news_stats, government_data, policy_stats, market_data = fetch_all_data(quick_mode=True)
            
            # Analyze data
            (sentiment_analysis, policy_analysis, market_health, sector_rankings,
             ticker_rankings, price_changes, current_prices, sentiment_scores, 
             sentiment_details, multi_ticker_articles, cross_ticker_analysis) = analyze_all_data(news_data, government_data, market_data)
            
            # Update panels
            summary_panel = self.query_one("#summary-panel", SummaryPanel)
            summary_panel.update_data(sentiment_analysis, policy_analysis, market_health)
            
            tickers_panel = self.query_one("#tickers-panel", TickersPanel)
            tickers_panel.update_data(sector_rankings, ticker_rankings, price_changes, current_prices)
            
            multi_ticker_panel = self.query_one("#multi-ticker-panel", MultiTickerPanel)
            multi_ticker_panel.update_data(multi_ticker_articles, cross_ticker_analysis)
            
            news_panel = self.query_one("#news-panel", NewsPanel)
            news_panel.update_data(news_data, sentiment_scores, sentiment_details, multi_ticker_articles)
            
        except Exception as e:
            # Handle errors gracefully
            self.notify(f"Error updating data: {str(e)}", severity="error")


def run_textual_dashboard():
    """Run the Textual dashboard"""
    app = FinancialDashboard()
    app.run()


if __name__ == "__main__":
    run_textual_dashboard()
